import { device } from 'detox';

import {
  expectElementNotVisible,
  expectElementVisible,
  expectIdToHaveText,
  initData,
  sleep,
  swipe,
  tapId,
  tapText,
  typeToTextField,
} from './step-definition';

const ASKER = {
  isoCode: 'VN',
  phone: '0834567890',
  name: 'Asker',
  type: 'ASKER',
  status: 'ACTIVE',
  oldUser: true,
};

const SURVEY_1 = {
  isoCode: 'VN',
  data: {
    _id: 'xxx',
    name: 'FIRST_DONE_TASK',
    title: {
      vi: 'Vui lòng đánh giá mức độ hài lòng của bạn về Kỹ năng của Tasker',
      en: "Please rate your satisfaction level with Tasker's skills",
      ko: '작업자의 솜씨에 대한 만족도를 평가해주세요',
      th: 'กรุณาให้คะแนนระดับความพึงพอใจของคุณกับความเชี่ยวชาญของ Tasker',
      id: "Mohon berikan penilaian tentang kemampuan Tasker's",
    },
    status: 'ACTIVE',
    reward: {
      title: {
        vi: 'xxx',
        en: 'xxx',
      },
      content: {
        vi: 'xxx',
        en: 'xxx',
      },
      note: {
        vi: 'xxx',
        en: 'xxx',
      },
      promotionPrefix: 'xxx',
      promotionValue: 30000.0,
      promotionType: 'MONEY',
      image: 'xx',
      period: 30.0,
    },
    questions: [
      {
        question: {
          vi: 'Vui lòng đánh giá mức độ hài lòng của bạn về Kỹ năng của Tasker',
          en: "Please rate your satisfaction level with Tasker's skills",
          ko: '작업자의 솜씨에 대한 만족도를 평가해주세요',
          th: 'กรุณาให้คะแนนระดับความพึงพอใจของคุณกับความเชี่ยวชาญของ Tasker',
          id: "Mohon berikan penilaian tentang kemampuan Tasker's",
        },
        type: 'RATING',
        answers: [],
      },
      {
        question: {
          vi: 'Vui lòng chia sẻ nhu cầu sử dụng dịch vụ của bạn',
          en: 'Please share your service usage needs',
          ko: '서비스 사용 필요에 대해 알려주세요',
          th: 'กรุณาบอกความต้องการในการใช้บริการของคุณ',
          id: 'Mohon informasikan seberapa sering Anda menggunakan service kebersihan',
        },
        type: 'SINGLE',
        answers: [
          {
            vi: 'Trên 3 lần/tuần',
            en: 'xxx',
          },
          {
            vi: 'Khoản 1-2 lần/tuần',
            en: 'xxx',
          },
          {
            vi: 'Khoản 1-2 lần/tháng ',
            en: 'xxx',
          },
          {
            vi: 'Không cố định, chỉ sử dụng khi cần',
            en: 'xxx',
          },
        ],
      },
      {
        question: {
          vi: 'Bạn biết đến bTaskee qua phương tiện nào?',
          en: 'How do you know bTaskee?',
          ko: 'xxx',
          th: 'xxx',
          id: 'xxx',
        },
        type: 'MULTIPLE',
        answers: [
          {
            vi: 'Google',
            en: 'xxx',
          },
          {
            vi: 'Facebook',
            en: 'xxx',
          },
          {
            vi: 'Tiktok',
            en: 'xxx',
          },
          {
            vi: 'Youtube',
            en: 'xxx',
          },
          {
            vi: 'App Store',
            en: 'xxx',
          },
          {
            vi: 'Thấy các Cộng Tác Viên',
            en: 'xxx',
          },
          {
            vi: 'Bạn bè giới thiệu',
            en: 'xxx',
          },
          {
            vi: 'Không nhớ',
            en: 'xxx',
          },
          {
            vi: 'Khác',
            en: 'xxx',
          },
        ],
      },
      {
        question: {
          vi: 'Bạn có những góp ý gì thêm cho bTaskee để nâng cao chất lượng dịch vụ không?',
          en: "Do you have any recommendations for the improvement of bTaskee's services?",
          ko: 'xxx',
          th: 'xxx',
          id: 'xxx',
        },
        type: 'INPUT',
        answers: [],
      },
    ],
  },
};

const SURVEY_2 = {
  ...SURVEY_1,
  data: {
    ...SURVEY_1.data,
    reward: {
      ...SURVEY_1.data.reward,
      promotionServiceId: 'pcZRQ6PqmjrAPe5gt',
    },
  },
};

const NOTIFICATION = {
  phone: ASKER.phone,
  isoCode: ASKER.isoCode,
  description:
    'Nhận ngay mã ưu đãi 50% khi tham gia khảo sát chất lượng dịch vụ của bTaskee.',
  type: 31.0,
  title: 'Tham gia khảo sát nhanh cùng bTaskee!',
  data: {
    surveyId: SURVEY_1.data._id,
  },
  navigateTo: 'SurveyDetail',
};

describe('FILE: e2e/a-vietnam/flow-test/survey/survey.spec.js - Login Asker', () => {
  beforeEach(async () => {
    await initData('resetData');
    await initData('user/createUser', [ASKER]);
    await initData('notification/send', [NOTIFICATION]);
  });

  it('LINE 208 - Asker send survey promotion apply all service', async () => {
    await initData('survey/insert', [SURVEY_1]);
    await tapId('Tab_Notification');
    await tapId('tabNotification');
    await tapText(NOTIFICATION.title);
    await expectElementVisible('Khảo sát nhanh', 'text');
    await expectElementVisible('Câu hỏi 1', 'text');
    await tapId('ratingOption-4');
    await sleep(1000);
    await expectElementNotVisible('Câu hỏi 1', 'text');
    await expectElementVisible('Câu hỏi 2', 'text');
    await tapId('singleOption-0');
    await sleep(1000);
    await expectElementNotVisible('Câu hỏi 2', 'text');
    await expectElementVisible('Câu hỏi 3', 'text');
    await tapId('multipleOption-0');
    await tapId('multipleOption-1');
    await tapId('multipleOption-2');
    await swipe('surveyDetailScroll', 'up');
    await typeToTextField('inputOption', 'Hay');
    await tapId('btnConfirmInputOption');
    await tapId('btnSendSurvey');
    await expectElementVisible('(Áp dụng cho tất cả dịch vụ)', 'text');
    await tapId('useNowBtn');
    await sleep(1000);
    // await postTask('postTaskServiceOFFICE_CLEANING');
    await tapText('Dịch vụ theo Buổi/Ngày');
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await expectIdToHaveText('originPrice', '208,200 VND');
    await expectIdToHaveText('price', '178,200 VND');
    await device.reloadReactNative();
    await tapId('Tab_Notification');
    await tapId('tabNotification');
    await tapText(NOTIFICATION.title);
    await expectElementVisible('bTaskee đã nhận được phản hồi của bạn', 'text');
  });

  it('LINE 235 - Asker send survey promotion apply cleaning service', async () => {
    await initData('survey/insert', [SURVEY_2]);
    await tapId('Tab_Notification');
    await tapId('tabNotification');
    await tapText(NOTIFICATION.title);
    await expectElementVisible('Khảo sát nhanh', 'text');
    await expectElementVisible('Câu hỏi 1', 'text');
    await tapId('ratingOption-4');
    await sleep(1000);
    await expectElementNotVisible('Câu hỏi 1', 'text');
    await expectElementVisible('Câu hỏi 2', 'text');
    await tapId('singleOption-0');
    await sleep(1000);
    await expectElementNotVisible('Câu hỏi 2', 'text');
    await expectElementVisible('Câu hỏi 3', 'text');
    await tapId('multipleOption-0');
    await tapId('multipleOption-1');
    await tapId('multipleOption-2');
    await swipe('surveyDetailScroll', 'up');
    await typeToTextField('inputOption', 'Hay');
    await tapId('btnConfirmInputOption');
    await tapId('btnSendSurvey');
    await expectElementVisible('(Áp dụng cho dịch vụ Dọn dẹp nhà)', 'text');
    await tapId('useNowBtn');
    await sleep(1000);
    await tapId('btnNextStep2');
    await tapId('btnNextStep3');
    await expectIdToHaveText('originPrice', '210,000 VND');
    await expectIdToHaveText('price', '180,000 VND');
    await tapText('Đăng việc');
    await tapText('Theo dõi công việc');
  });
});
