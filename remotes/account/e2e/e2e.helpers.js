const {
  waitForElement,
  tapId,
  expectElementVisible,
  typeToTextField,
  swipe,
} = require('./step-definition');

class E2EHelpers {
  static login = async (phone, password = '123456') => {
    try {
      await waitForElement('Tab_Account', 8000);
      await expectElementVisible('Tab_Account');
      await tapId('Tab_Account');

      await waitForElement('SignInBtn', 8000);
      await expectElementVisible('SignInBtn');
      await tapId('SignInBtn');

      await waitForElement('PhoneNumberInput', 8000);
      await expectElementVisible('PhoneNumberInput');
      await typeToTextField('PhoneNumberInput', phone);

      await typeToTextField('PasswordInput', password);

      await tapId('SubmitLogin');
    } catch (error) {}
  };

  static logout = async () => {
    await waitForElement('Tab_Account', 8000);
    await expectElementVisible('Tab_Account');
    await tapId('Tab_Account');
    await swipe('AccountScroll', 'up');

    await waitForElement('SettingBtn', 8000);
    await expectElementVisible('SettingBtn');
    await tapId('SettingBtn');

    await waitForElement('ShowLogOutModal', 8000);
    await expectElementVisible('ShowLogOutModal');
    await tapId('ShowLogOutModal');

    await waitForElement('ConfirmLogOut', 8000);
    await expectElementVisible('ConfirmLogOut');
    await tapId('ConfirmLogOut');
  };
}

module.exports = { E2EHelpers };
