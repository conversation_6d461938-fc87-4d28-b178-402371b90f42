import React, { useCallback, useMemo } from 'react';
import {
  AccountRouteName,
  B2BRouteName,
  BlockView,
  BRewardRouteName,
  ChatRouteName,
  Colors,
  ComboVoucherRouteName,
  ConditionView,
  CText,
  EndpointKeys,
  getIsoCodeGlobal,
  IconAssets,
  imgSuccess,
  ISO_CODE,
  NavigationService,
  RouteName,
  showPriceAndCurrency,
  SizedBox,
  Spacing,
  StatusBusinessAccount,
  useApiQuery,
  useCheckSignIn,
  useUserStore,
} from '@btaskee/design-system';
import { MenuItemProps } from '@src/components/MenuItem';
import { useAppNavigation, useI18n } from '@src/hooks';
import { isEmpty } from 'lodash-es';

import styles from './styles';

const useAccountScreen = () => {
  const navigation = useAppNavigation();
  const { t } = useI18n();
  const { user } = useUserStore();
  const { onHandleCheckSignIn, isAuthenticated } = useCheckSignIn();
  const { data: memberInfo } = useApiQuery({
    key: EndpointKeys.getMemberInfo,
    queryKey: ['getMemberInfo', isAuthenticated],
    params: {},
    options: {
      enabled: isAuthenticated,
      retry: false,
    },
  });

  const isNewUser = useMemo(() => {
    return (
      !isEmpty(user) &&
      isEmpty(user?.business) &&
      getIsoCodeGlobal() === ISO_CODE.VN
    );
  }, [user]);
  const isShowItemBusses = useMemo(() => {
    return (
      !isEmpty(user) &&
      (user?.isBusinessAdmin || user?.isBusinessCreator) &&
      getIsoCodeGlobal() === ISO_CODE.VN
    );
  }, [user]);

  const ACCOUNT_MENU: MenuItemProps[] = useMemo(() => {
    const normalMenu = [
      {
        icon: IconAssets.icUser,
        title: 'PERSONAL_PROFILE',
        testID: 'PersonalProfileBtn',
        onPress: () => {
          NavigationService.navigate(RouteName.TabAccountNavigator, {
            screen: AccountRouteName.UserEditProfile,
          });
        },
      },
      {
        icon: IconAssets.icLocation,
        title: 'SAVED_ADDRESS',
        testID: 'SavedAddressBtn',
        onPress: () => {
          NavigationService.navigate(RouteName.TabAccountNavigator, {
            screen: AccountRouteName.SavedAddress,
          });
        },
      },
      {
        icon: IconAssets.icHistory,
        title: 'TRANSACTION_HISTORY',
        testID: 'TransactionHistoryBtn',
        onPress: () => {
          NavigationService.navigate(RouteName.TabAccountNavigator, {
            screen: AccountRouteName.BPay,
            params: { tabIndex: 2 },
          });
        },
      },
      {
        icon: IconAssets.icGiftFill,
        title: 'MY_OFFER',
        testID: 'MyOfferBtn',
        onPress: () => {
          NavigationService.navigate(RouteName.BReward, {
            screen: BRewardRouteName.MyReward,
          });
        },
      },
      {
        icon: IconAssets.icFavoriteTasker,
        title: 'FAVORITE_TASKER',
        testID: 'FavoriteTaskerBtn',
        onPress: () => {
          NavigationService.navigate(RouteName.TabAccountNavigator, {
            screen: AccountRouteName.FavoriteTaskerScreen,
          });
        },
      },
      {
        icon: IconAssets.icJournalXFill,
        title: 'BLOCK_LIST',
        testID: 'BlockListBtn',
        onPress: () => {
          NavigationService.navigate(RouteName.TabAccountNavigator, {
            screen: AccountRouteName.TaskerBlacklistScreen,
          });
        },
      },
    ];
    const businessMenu = {
      testID: 'btnBusinessAccountItem',
      icon: IconAssets.icBuildingPeople,
      title: user?.business
        ? 'BUSINESS_ACCOUNT.TITLE'
        : 'BUSINESS_ACCOUNT.CREATE',
      onPress: () => {
        if (user?.business) {
          switch (user?.business?.status) {
            case StatusBusinessAccount.ACTIVE:
              // nếu là người tạo thì vào trang info register business account
              if (user?.isBusinessCreator) {
                return NavigationService.navigate(RouteName.B2B, {
                  screen: B2BRouteName.InfoRegisterBusinessAccount,
                  params: { isNotShowStatus: true },
                });
              }
              // nếu là admin thì vào trang business profile
              if (user?.isBusinessAdmin) {
                return NavigationService.navigate(RouteName.B2B, {
                  screen: B2BRouteName.BusinessProfile,
                });
              }
              return;

            default:
              NavigationService.navigate(RouteName.B2B, {
                screen: B2BRouteName.SuccessScreen,
                params: {
                  title: t('BUSINESS_ACCOUNT.REGISTER_BUSINESS_SUCCESS'),
                  image: imgSuccess,
                  onPress: () =>
                    NavigationService.replace(RouteName.B2B, {
                      screen: B2BRouteName.InfoRegisterBusinessAccount,
                    }),
                  titleButtonBottom: t('BUSINESS_ACCOUNT.VIEW_INFO_REGISTER'),
                },
              });
              return;
          }
        }
        NavigationService.navigate(RouteName.B2B, {
          screen: B2BRouteName.IntroRegisterBusinessAccount,
        });
      },
      footerComponent: (
        <ConditionView
          condition={Boolean(
            user?.business?.status === StatusBusinessAccount.ACTIVE,
          )}
          viewTrue={
            <BlockView
              overflow="hidden"
              margin={{ top: Spacing.SPACE_12 }}
              padding={{ top: Spacing.SPACE_12 }}
            >
              <BlockView
                row
                horizontal
                jBetween
              >
                <CText color={Colors.neutral300}>
                  {t('BUSINESS_ACCOUNT.QUANTITY')}
                </CText>
                <CText>
                  {user?.business?.numberOfMembers
                    ? t('BUSINESS_ACCOUNT.MEMBER', {
                        count: user?.business?.numberOfMembers,
                      })
                    : t('BUSINESS_ACCOUNT.EMPTY_MEMBER')}
                </CText>
              </BlockView>
              <SizedBox height={Spacing.SPACE_08} />
              <BlockView
                row
                horizontal
                jBetween
              >
                <CText color={Colors.neutral300}>
                  {t('BUSINESS_ACCOUNT.BALANCE')}
                </CText>
                <CText>{showPriceAndCurrency(user?.business?.bPay)}</CText>
              </BlockView>
              <BlockView style={styles.border} />
            </BlockView>
          }
        />
      ),
    };
    // chỉ hiển thị khi user là business member
    if (isNewUser || isShowItemBusses) {
      return [...normalMenu, businessMenu];
    }
    return normalMenu;
  }, [
    user?.business,
    user?.isBusinessCreator,
    user?.isBusinessAdmin,
    t,
    isNewUser,
    isShowItemBusses,
  ]);

  const SUPPORT_MENU: MenuItemProps[] = useMemo(
    () => [
      {
        icon: IconAssets.icHelp,
        title: 'HELP',
        testID: 'HelpBtn',
        onPress: () => {
          NavigationService.navigate(RouteName.TabAccountNavigator, {
            screen: AccountRouteName.Help,
          });
        },
      },
      {
        icon: IconAssets.icSetting,
        title: 'SETTING',
        testID: 'SettingBtn',
        onPress: () => {
          NavigationService.navigate(RouteName.TabAccountNavigator, {
            screen: AccountRouteName.Setting,
          });
        },
      },
      {
        icon: IconAssets.icBee,
        title: 'ABOUT_BTASKEE',
        testID: 'AboutBtaskeeBtn',
        onPress: () => {
          NavigationService.navigate(RouteName.TabAccountNavigator, {
            screen: AccountRouteName.AboutbTaskee,
          });
        },
      },
    ],
    [],
  );

  const UTILITY_MENU: MenuItemProps[] = useMemo(
    () => [
      {
        icon: IconAssets.icPay,
        title: 'B_PAY',
        testID: 'BPayBtn',
        routeName: RouteName.TabAccountNavigator,
        routeNameInApp: AccountRouteName.BPay,
        onPress: () =>
          onHandleCheckSignIn(() => {
            NavigationService.navigate(RouteName.TabAccountNavigator, {
              screen: AccountRouteName.BPay,
            });
          }),
      },
      {
        icon: IconAssets.icBrewards,
        title: 'B_REWARDS',
        testID: 'BRewardsBtn',
        onPress: () =>
          onHandleCheckSignIn(() => {
            NavigationService.navigate(RouteName.BReward, {
              screen: BRewardRouteName.Home,
            });
          }),
      },
      {
        icon: IconAssets.icPromote,
        title: 'VOUCHER_PACKAGE',
        testID: 'VoucherPackageBtn',
        routeNameInApp: ComboVoucherRouteName.ListComboVoucher,
        onPress: () => {
          NavigationService.navigate(RouteName.ComboVoucher, {
            screen: ComboVoucherRouteName.ListComboVoucher,
          });
        },
      },
      {
        icon: IconAssets.icStarts,
        title: 'INVITE_FRIEND',
        testID: 'InviteFriendBtn',
        onPress: () => {
          NavigationService.navigate(RouteName.TabAccountNavigator, {
            screen: AccountRouteName.Referral,
          });
        },
      },
    ],
    [onHandleCheckSignIn],
  );

  const onPressChat = useCallback(() => {
    onHandleCheckSignIn(() =>
      NavigationService.navigate(RouteName.ChatManagement, {
        screen: ChatRouteName.Chat,
      }),
    );
  }, [onHandleCheckSignIn]);

  const onPressMember = useCallback(() => {
    NavigationService.navigate(RouteName.BReward, {
      screen: BRewardRouteName.MemberDetail,
    });
  }, []);

  return {
    t,
    user,
    isAuthenticated,
    navigation,
    ACCOUNT_MENU,
    SUPPORT_MENU,
    UTILITY_MENU,
    memberInfo,
    onPressChat,
    onPressMember,
  };
};

export default useAccountScreen;
