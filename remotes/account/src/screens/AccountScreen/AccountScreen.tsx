import '../../i18n';

import React from 'react';
import {
  Avatar,
  BlockView,
  BorderRadius,
  Colors,
  CText,
  DeviceHelper,
  FontSizes,
  getTextWithLocale,
  Icon,
  IconImage,
  ScrollView,
  Spacing,
  TouchableOpacity,
  useCheckSignIn,
} from '@btaskee/design-system';

import { Menu } from '@components';
import { icMessage, icTier } from '@images';

import Feedback from './components/Feedback';
import useAccountScreen from './hook';
import styles from './styles';

const AccountScreen = () => {
  const {
    onPressChat,
    t,
    ACCOUNT_MENU,
    SUPPORT_MENU,
    UTILITY_MENU,
    user,
    memberInfo,
    onPressMember,
    isAuthenticated,
  } = useAccountScreen();
  const { onPressSignIn } = useCheckSignIn();

  const renderButtonLogin = () => {
    return (
      <TouchableOpacity
        onPress={onPressSignIn}
        testID="SignInBtn"
      >
        <BlockView
          row
          horizontal
          alignSelf="flex-start"
          radius={BorderRadius.RADIUS_08}
          backgroundColor={Colors.green500}
          padding={{
            vertical: Spacing.SPACE_04,
            horizontal: Spacing.SPACE_12,
          }}
          margin={{ top: Spacing.SPACE_04 }}
        >
          <CText
            bold
            color={Colors.neutralWhite}
            size={FontSizes.SIZE_14}
          >
            {t('SIGN_IN')}
          </CText>
          <Icon
            name="icNext"
            size={20}
            color={Colors.neutralWhite}
          />
        </BlockView>
      </TouchableOpacity>
    );
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
      testID="AccountScroll"
    >
      <BlockView
        inset={'top'}
        style={styles.safeAreaView}
      >
        <BlockView
          row
          jBetween
          horizontal
          gap={Spacing.SPACE_08}
        >
          <CText
            size={FontSizes.SIZE_18}
            bold
            color={Colors.neutral800}
          >
            {t('TAB_ACCOUNT')}
          </CText>
          <TouchableOpacity onPress={onPressChat}>
            <IconImage
              source={icMessage}
              style={styles.iconMessage}
            />
          </TouchableOpacity>
        </BlockView>

        <BlockView
          row
          horizontal
          gap={Spacing.SPACE_08}
        >
          <Avatar
            avatar={isAuthenticated ? user?.avatar : ''}
            size={56 * DeviceHelper.WIDTH_RATIO}
          />
          <BlockView gap={Spacing.SPACE_04}>
            {isAuthenticated ? (
              <BlockView gap={Spacing.SPACE_04}>
                <CText
                  semiBold
                  size={FontSizes.SIZE_18}
                  color={Colors.neutral800}
                >
                  {user?.name}
                </CText>
                {!!memberInfo && (
                  <TouchableOpacity
                    onPress={onPressMember}
                    row
                    horizontal
                    gap={Spacing.SPACE_04}
                    radius={BorderRadius.RADIUS_FULL}
                    padding={{
                      horizontal: Spacing.SPACE_08,
                      vertical: Spacing.SPACE_04,
                    }}
                    backgroundColor={'#00BFF1'}
                  >
                    <IconImage
                      source={icTier}
                      size={14}
                    />
                    <CText
                      color={Colors.neutralWhite}
                      semiBold
                    >
                      {getTextWithLocale(memberInfo?.currentRankInfo?.text)}
                    </CText>
                  </TouchableOpacity>
                )}
              </BlockView>
            ) : (
              renderButtonLogin()
            )}
          </BlockView>
        </BlockView>

        <BlockView gap={Spacing.SPACE_24}>
          {isAuthenticated && (
            <Menu
              title={t('TAB_ACCOUNT')}
              data={ACCOUNT_MENU}
            />
          )}

          <Menu
            title={t('UTILITY')}
            data={UTILITY_MENU}
          />
          <Menu
            title={t('SUPPORT')}
            data={SUPPORT_MENU}
          />
        </BlockView>

        {isAuthenticated && <Feedback />}
      </BlockView>
    </ScrollView>
  );
};

export default AccountScreen;
