import { useCallback, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import {
  AccountRouteName,
  Alert,
  COUNTRIES,
  EndpointKeys,
  IStateAlert,
  ToastHelpers,
  useApiMutation,
  useUserStore,
  validEmail,
} from '@btaskee/design-system';
import { yupResolver } from '@hookform/resolvers/yup';
import { useAppNavigation, useI18n } from '@src/hooks';
import * as yup from 'yup';

const useUserEditProfileScreen = () => {
  const { t } = useI18n();
  const { user, getUser } = useUserStore();
  const navigation = useAppNavigation();
  const emailUser = useMemo(() => user?.emails?.[0], [user]);
  const country = useMemo(
    () => COUNTRIES.find((item) => item.countryCode === user?.countryCode),
    [user?.countryCode],
  );

  const editProfileSchema = useMemo(
    () =>
      yup.object().shape({
        name: yup.string().trim().required(t('FULL_NAME_REQUIRED')),
        email: yup
          .string()
          .trim()
          .defined()
          .test('is-valid-email', t('MESSAGE_EMAIL_INVALID'), (value) => {
            if (!value) return true;
            return validEmail(value);
          }),
      }),
    [t],
  );

  const {
    control,
    handleSubmit,
    reset,
    watch,
    formState: { isValid, isDirty },
  } = useForm({
    mode: 'onChange',
    defaultValues: {
      name: user?.name || '',
      email: emailUser?.address || '',
    },
    resolver: yupResolver(editProfileSchema),
  });

  const email = watch('email');

  const { mutate: updateUserInfoAPI, isPending } = useApiMutation({
    key: EndpointKeys.updateUserProfile,
    options: {
      onSuccess: (_, variables) => {
        getUser();
        reset(variables);
        ToastHelpers.showSuccess({
          message: t('UPDATE_PROFILE_SUCCESS'),
          position: 'top',
        });
      },
      onError: (error: any) => {
        const alertObj: IStateAlert = {
          title: t('DIALOG_TITLE_INFORMATION'),
          message: t('ERROR_TRY_AGAIN'),
          actions: [{ text: t('CLOSE') }],
        };
        switch (error?.data?.code) {
          case 'EMAIL_EXIST':
            alertObj.message = t('MESSAGE_EMAIL_EXIST');
            break;
        }
        Alert.alert.open(alertObj);
      },
    },
  });

  const handleUpdateProfile = useCallback(() => {
    handleSubmit((data) => {
      updateUserInfoAPI(data);
    })();
  }, [handleSubmit, updateUserInfoAPI]);

  const handleDeleteAccount = useCallback(() => {
    navigation.navigate(AccountRouteName.DeleteAccount);
  }, [navigation]);

  return {
    navigation,
    t,
    control,
    phone: user?.phone || '',
    country,
    isVerifiedEmail: emailUser?.verified,
    isShowVerifyEmail: emailUser?.address === email,
    isSubmitDisabled:
      !isValid || !isDirty || isPending || (!!emailUser?.address && !email),
    handleUpdateProfile,
    handleDeleteAccount,
  };
};

export default useUserEditProfileScreen;
