import React, { memo, useCallback } from 'react';
import {
  <PERSON>ert,
  Colors,
  CText,
  EndpointKeys,
  Icon,
  IStateAlert,
  TouchableOpacity,
  useApiMutation,
} from '@btaskee/design-system';
import { debounce } from 'lodash-es';

import { useI18n } from '@hooks';

export const LIST_ERROR_API_VERIFY_EMAIL = {
  EMAIL_VERIFIED: 'EMAIL_VERIFIED',
  USER_ID_REQUIRED: 'USER_ID_REQUIRED',
  CANNOT_SEND_EMAIL: 'CANNOT_SEND_EMAIL',
  USER_NOT_FOUND: 'USER_NOT_FOUND',
};

interface EmailVerificationIconProps {
  isVerified?: boolean;
}

const EmailVerificationIcon = ({ isVerified }: EmailVerificationIconProps) => {
  const { t } = useI18n();

  const { mutate: sendVerifyEmailAPI } = useApiMutation({
    key: EndpointKeys.sendVerifyEmail,
    options: {
      onSuccess: () => {
        Alert.alert.open({
          title: t('DIALOG_TITLE_INFORMATION'),
          message: t('RESEND_VERIFICATION_EMAIL_OK'),
          actions: [{ text: t('CLOSE') }],
        });
      },
      onError: (error: any) => {
        const alertObj: IStateAlert = {
          title: t('DIALOG_TITLE_INFORMATION'),
          message: t('ERROR_TRY_AGAIN'),
          actions: [{ text: t('CLOSE') }],
        };
        switch (error?.data?.code) {
          case LIST_ERROR_API_VERIFY_EMAIL.EMAIL_VERIFIED:
            alertObj.message = t('MESSAGE_EMAIL_EXIST');
            break;
          case LIST_ERROR_API_VERIFY_EMAIL.CANNOT_SEND_EMAIL:
            alertObj.message = t('CANNOT_SEND_EMAIL');
            break;
        }
        Alert.alert.open(alertObj);
      },
    },
  });

  const handleVerifyEmail = useCallback(() => {
    sendVerifyEmailAPI({});
  }, [sendVerifyEmailAPI]);

  if (isVerified) {
    return (
      <Icon
        name="icCheck2All"
        size={24}
        color={Colors.green500}
      />
    );
  }

  return (
    <TouchableOpacity
      disabled={isVerified}
      onPress={debounce(handleVerifyEmail, 300)}
    >
      <CText
        center
        color={Colors.green500}
      >
        {t('VERIFICATION_BUTTON')}
      </CText>
    </TouchableOpacity>
  );
};

export default memo(EmailVerificationIcon);
