import React from 'react';
import {
  BlockView,
  Card,
  Colors,
  CText,
  DeviceHelper,
  KeyboardAware,
  PaymentMethodBlock,
  PrimaryButton,
  Spacing,
  TYPE_OF_PAYMENT,
} from '@btaskee/design-system';
import Header from '@src/components/Header';
import { debounce } from 'lodash-es';

import DebtAmountDisplay from './components/DebtAmountDisplay';
import QuickAmountSelector from './components/QuickAmountSelector';
import TermsOfUse from './components/TermsOfUse';
import TopUpHeaderContext from './components/TopUpHeaderContext';
import useTopupScreen, { TopupScreenProps } from './hook';
import styles from './styles';

export const TopupScreen: React.FC<TopupScreenProps> = (props) => {
  const {
    t,
    topupValue,
    inputValue,
    topUpPaymentMethod,
    submitButtonDisabled,
    keyboardHeight,
    footerHeight,
    isShow,
    debtAmount,
    isDebtPayment,
    onChangeAmount,
    handleQuickAmountClick,
    submitTopup,
    setIsShow,
    isPaymentMethodListEmpty,
    footerRef,
    setTopUpPaymentMethod,
  } = useTopupScreen(props);

  if (isPaymentMethodListEmpty) {
    return (
      <BlockView
        flex
        center
      >
        <CText bold>{t('COMING_SOON')}</CText>
      </BlockView>
    );
  }

  return (
    <BlockView flex>
      <Header
        height={127 * DeviceHelper.WIDTH_RATIO}
        title={t('TOP_UP')}
      />
      <KeyboardAware
        contentContainerStyle={styles.content}
        keyboardShouldPersistTaps="handled"
      >
        <Card>
          <TopUpHeaderContext
            onChangeAmount={onChangeAmount}
            inputValue={inputValue}
            setIsShow={setIsShow}
            isShow={isShow}
          />
          <BlockView
            width={'100%'}
            height={1}
            backgroundColor={Colors.neutral100}
            margin={{ top: Spacing.SPACE_32, bottom: Spacing.SPACE_04 }}
          />
          <PaymentMethodBlock
            type={TYPE_OF_PAYMENT.topUp}
            paymentMethodOptions={{
              currentPaymentMethod: topUpPaymentMethod,
            }}
            onChangePaymentMethod={setTopUpPaymentMethod}
            isHidePromotion={true}
          />
          {debtAmount > 0 && <DebtAmountDisplay debtAmount={debtAmount} />}

          {isDebtPayment && <TermsOfUse />}
        </Card>
      </KeyboardAware>

      <BlockView
        padding={{ top: Spacing.SPACE_16, horizontal: Spacing.SPACE_16 }}
        backgroundColor={Colors.neutralWhite}
        inset={'bottom'}
        ref={footerRef}
      >
        <PrimaryButton
          title={t('TOP_UP_SUBMIT')}
          onPress={debounce(submitTopup, 300)}
          disabled={submitButtonDisabled}
        />
      </BlockView>

      {keyboardHeight > 0 && isShow && (
        <QuickAmountSelector
          selectedValue={topupValue}
          onSelectAmount={handleQuickAmountClick}
          keyboardHeight={keyboardHeight}
          footerHeight={footerHeight}
        />
      )}
    </BlockView>
  );
};
