import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Keyboard } from 'react-native';
import {
  AccountRouteName,
  AccountStackScreenProps,
  Alert,
  BlockView,
  EndpointKeys,
  IObjectText,
  IPaymentMethodInfo,
  ISO_CODE,
  PAYMENT_METHOD,
  PaymentService,
  showPriceAndCurrency,
  useApiMutation,
  useAppLoading,
  useAppStore,
  useSettingsStore,
} from '@btaskee/design-system';
import { get } from 'lodash-es';

import { useI18n } from '@hooks';

export type TopupScreenProps = AccountStackScreenProps<AccountRouteName.Topup>;

const ROUNDING_UNIT = {
  [ISO_CODE.VN]: 1000,
  [ISO_CODE.TH]: 1,
  [ISO_CODE.ID]: 1000,
  [ISO_CODE.MY]: 1,
};

export interface IPaymentMethod {
  label?: string;
  icon?: any;
  name?: string;
  bankInfo?: {
    name?: string;
    text?: IObjectText;
    value?: string;
  };
  cardInfo?: {
    type?: string;
    number?: string;
    _id?: string;
  };
  walletInfo?: {
    phoneNumber?: string;
  };
  value?: PAYMENT_METHOD;
}

export const MIN_TOP_UP = {
  [ISO_CODE.VN]: 200000, // Min top up VND
  [ISO_CODE.TH]: 200, // Min top up THB
  [ISO_CODE.ID]: 200000, // Min top up IDR
  [ISO_CODE.MY]: 100, // Min top up MYR
};

const useTopupScreen = ({ navigation, route }: TopupScreenProps) => {
  const { t } = useI18n();
  const { isoCode } = useAppStore();
  const { hideAppLoading, showAppLoading } = useAppLoading();
  const { settings } = useSettingsStore();

  const isDebtPayment = route.params?.isDebtPayment ?? false;
  const debtAmount = Math.abs(route.params?.amount ?? 0);

  // State
  const [topupValue, setTopupValue] = useState(isDebtPayment ? debtAmount : 0);
  const [inputValue, setInputValue] = useState<string>('');
  const [topUpPaymentMethod, setTopUpPaymentMethod] = useState<
    IPaymentMethodInfo | undefined
  >();
  const footerRef = useRef<BlockView>(null);
  const [submitButtonDisabled, setSubmitButtonDisabled] = useState(true);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const [footerHeight, setFooterHeight] = useState(0);
  const [isShow, setIsShow] = useState(false);

  // API
  const { mutate } = useApiMutation({
    key: EndpointKeys.topUpCredit,
    options: {
      onMutate: () => showAppLoading(),
      onSuccess: (result) => {
        hideAppLoading();
        PaymentService.onTopUpSuccess({
          paymentMethod: topUpPaymentMethod,
          data: result,
        });
      },
      onError: (error) => {
        hideAppLoading();
        setSubmitButtonDisabled(false);
        if (error?.data?.code === 'TOP_UP_CREDIT_MAXIMUM') {
          return Alert.alert.open(
            {
              title: t('DIALOG_TITLE_INFORMATION'),
              message: t('TOP_UP_ERROR_MAXIMUM_VALUE', {
                value: showPriceAndCurrency(error?.data.data?.amount),
              }),
              actions: [{ text: t('CLOSE') }],
            },
            true,
          );
        } else {
          return Alert.alert.open({
            title: t('DIALOG_TITLE_ERROR'),
            message: t('TOP_UP_PAYMENT_FAILED'),
            actions: [{ text: t('CLOSE'), onPress: navigation.goBack }],
          });
        }
      },
    },
  });

  const paymentMethodList = useMemo(() => {
    return (
      settings?.settingSystem?.paymentMethods?.topUp?.filter(
        (item) => item.status === 'ACTIVE',
      ) ?? []
    );
  }, [settings?.settingSystem?.paymentMethods]);

  const minTopUpSetting = useMemo(() => {
    const minTopUpDefault = get(MIN_TOP_UP, `${isoCode}`, 0);
    return get(settings, 'settingSystem.minTopUp', minTopUpDefault);
  }, [settings, isoCode]);

  const isPaymentMethodListEmpty = useMemo(() => {
    return !paymentMethodList || paymentMethodList.length === 0;
  }, [paymentMethodList]);

  const onChangeAmount = useCallback(
    (masked: string, unmasked: string) => {
      if (unmasked === '0') return;

      setInputValue(unmasked);
      const newAmount = Math.floor(
        Number(unmasked) * ROUNDING_UNIT[isoCode as keyof typeof ROUNDING_UNIT],
      );
      setTopupValue(Number(newAmount));

      if (unmasked === '') {
        Keyboard.dismiss();
      }
    },
    [isoCode],
  );

  const handleQuickAmountClick = useCallback(
    (value: number) => {
      setTopupValue(value);
      const newAmount = Math.floor(
        value / ROUNDING_UNIT[isoCode as keyof typeof ROUNDING_UNIT],
      );
      setInputValue(newAmount.toString());
    },
    [isoCode],
  );

  const submitTopup = useCallback(async () => {
    // Validation
    if (
      !isDebtPayment &&
      !settings?.isTester &&
      topupValue &&
      Number(topupValue) < minTopUpSetting
    ) {
      return Alert.alert.open({
        title: t('DIALOG_TITLE_INFORMATION'),
        message: t('TOP_UP_ERROR_VALUE_NOT_ENOUGH', {
          t1: showPriceAndCurrency(minTopUpSetting),
        }),
        actions: [{ text: t('CLOSE') }],
      });
    }

    const requestData: any = {
      amount: topupValue,
      isoCode,
      payment:
        PaymentService.formatPaymentMethodInfoToParams(topUpPaymentMethod),
    };

    if (isDebtPayment) {
      requestData.isDebtPayment = true;
    }

    setSubmitButtonDisabled(true);
    mutate(requestData);
  }, [
    isDebtPayment,
    settings?.isTester,
    topupValue,
    minTopUpSetting,
    isoCode,
    topUpPaymentMethod,
    mutate,
  ]);

  // Effects
  useEffect(() => {
    if (isDebtPayment && debtAmount > 0) {
      setSubmitButtonDisabled(false);
      navigation.setOptions({ title: t('DEBT_PAYMENT_TITLE') });
    } else if (topUpPaymentMethod && topupValue) {
      setSubmitButtonDisabled(false);
    }
  }, [
    topUpPaymentMethod,
    topupValue,
    isDebtPayment,
    debtAmount,
    navigation,
    t,
  ]);

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      (event) => {
        setKeyboardHeight(event.endCoordinates.height);
        // Neu Android thi hien thi ngay tren footer
        if (footerRef.current) {
          footerRef.current.measure((x, y, width, height) => {
            setFooterHeight(height);
          });
        }
      },
    );

    return () => {
      keyboardDidShowListener.remove();
    };
  }, []);
  return {
    t,
    navigation,
    topupValue,
    inputValue,
    topUpPaymentMethod,
    submitButtonDisabled,
    keyboardHeight,
    footerHeight,
    isShow,
    debtAmount,
    isDebtPayment,
    minTopUpSetting,
    paymentMethodList,
    isoCode,
    onChangeAmount,
    handleQuickAmountClick,
    setTopUpPaymentMethod,
    submitTopup,
    setIsShow,
    isPaymentMethodListEmpty,
    footerRef,
  };
};

export default useTopupScreen;
