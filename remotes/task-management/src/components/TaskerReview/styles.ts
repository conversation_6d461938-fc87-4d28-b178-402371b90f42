import { StyleSheet } from 'react-native';
import { Colors, Spacing } from '@btaskee/design-system';

export const styles = StyleSheet.create({
  review: {
    backgroundColor: Colors.neutralWhite,
    paddingTop: Spacing.SPACE_32,
    marginTop: Spacing.SPACE_08,
  },
  
  txtPanel: {
    marginLeft: Spacing.SPACE_16,
    marginBottom: Spacing.SPACE_16,
    textTransform: 'uppercase',
  },
  
  wrapReview: {
    backgroundColor: Colors.neutralWhite,
    paddingHorizontal: Spacing.SPACE_16,
  },
  
  border: {
    borderBottomWidth: 1,
    borderColor: Colors.neutral100,
  },
  
  borderTop: {
    borderTopWidth: 1,
    borderColor: Colors.neutral100,
  },
  
  txtReview: {
    marginVertical: Spacing.SPACE_12,
    lineHeight: 20,
  },
});
