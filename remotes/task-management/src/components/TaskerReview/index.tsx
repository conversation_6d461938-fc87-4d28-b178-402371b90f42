/**
 * TaskerReview component for displaying tasker reviews
 * Migrated from legacy tasker-detail-reviews.js with modern design system components
 */
import React from 'react';
import {
  BlockView,
  Colors,
  CText,
  FontSizes,
  Spacing,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { styles } from './styles';

interface TaskerReviewProps {
  reviews: string[];
}

/**
 * TaskerReview component displays customer reviews for the tasker
 * Shows reviews in a list format with proper styling
 */
export const TaskerReview: React.FC<TaskerReviewProps> = ({ reviews }) => {
  const { t } = useI18n();

  // Don't render if no reviews
  if (!reviews || reviews.length <= 0) {
    return null;
  }

  return (
    <BlockView style={styles.review}>
      <CText
        style={styles.txtPanel}
        color={Colors.neutral800}
        size={FontSizes.SIZE_16}
        semiBold
      >
        {t('REVIEW_OF_TASKER')}
      </CText>
      
      <BlockView style={styles.wrapReview}>
        {reviews.map((review, index) => {
          const borderTop = index === 0 ? styles.borderTop : {};
          
          return (
            <BlockView
              style={[styles.border, borderTop]}
              key={index}
            >
              <CText
                semiBold
                style={styles.txtReview}
                color={Colors.neutral800}
                size={FontSizes.SIZE_14}
              >
                {`"${review}"`}
              </CText>
            </BlockView>
          );
        })}
      </BlockView>
    </BlockView>
  );
};
