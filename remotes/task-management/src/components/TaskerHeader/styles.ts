import { Dimensions, StyleSheet } from 'react-native';
import { Colors, Spacing } from '@btaskee/design-system';

const { width } = Dimensions.get('window');
const SIZE_AVATAR = Math.round(width / 4);
const SIZE_PREMIUM_BANNER = Math.round(width / 3);

export const styles = StyleSheet.create({
  header: {
    backgroundColor: Colors.neutralWhite,
    alignItems: 'center',
    paddingBottom: Spacing.SPACE_24,
    marginTop: Spacing.SPACE_08,
  },
  
  centerContainer: {
    alignItems: 'center',
  },
  
  imageAvatar: {
    marginVertical: Spacing.SPACE_16,
  },
  
  txtName: {
    textAlign: 'center',
    marginHorizontal: Spacing.SPACE_16,
  },
  
  rating: {
    paddingTop: Spacing.SPACE_16,
  },
  
  info: {
    backgroundColor: Colors.neutralWhite,
    borderWidth: 1,
    borderColor: Colors.neutral100,
    borderRadius: 18,
    paddingVertical: Spacing.SPACE_08,
    marginTop: Spacing.SPACE_16,
    flexDirection: 'row',
    alignItems: 'center',
  },
  
  line: {
    width: 1,
    backgroundColor: Colors.neutral200,
    height: '60%',
  },
  
  txtTask: {
    fontSize: 14,
  },
  
  wrapTaskInfo: {
    alignItems: 'center',
    paddingHorizontal: '5%',
    flexDirection: 'row',
    flex: 1,
    justifyContent: 'center',
  },
  
  txtHeaderNumber: {
    fontSize: 14,
  },
  
  banner: {
    marginTop: Spacing.SPACE_08,
  },
  
  premiumBanner: {
    width: SIZE_PREMIUM_BANNER,
    height: 32,
  },
});
