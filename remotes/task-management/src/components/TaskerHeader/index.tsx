/**
 * TaskerHeader component for TaskerDetail screen
 * Migrated from legacy tasker-detail-header.js with modern design system components
 */
import React, { useMemo } from 'react';
import { Dimensions } from 'react-native';
import {
  Avatar,
  BlockView,
  Colors,
  ConditionView,
  CText,
  FontSizes,
  IconImage,
  Rating,
  Spacing,
} from '@btaskee/design-system';

import { useI18n } from '@hooks';

import { styles } from './styles';

const { width } = Dimensions.get('window');
const SIZE_AVATAR = Math.round(width / 4);

interface TaskerHeaderProps {
  taskerDetail: {
    avatar?: string;
    avgRating?: string;
    taskDone?: number;
    name?: string;
    isPremiumTasker?: boolean;
    isFavouriteTasker?: boolean;
    numberOfReview?: number;
  };
  taskerSettings?: {
    avatarFrame?: any;
  };
}

/**
 * TaskerHeader component displays tasker avatar, name, rating, and statistics
 * Preserves exact UI design and functionality from legacy component
 */
export const TaskerHeader: React.FC<TaskerHeaderProps> = ({
  taskerDetail = {},
  taskerSettings = {},
}) => {
  const { t } = useI18n();
  const {
    avatar,
    avgRating,
    taskDone,
    name,
    isPremiumTasker,
    isFavouriteTasker,
    numberOfReview,
  } = taskerDetail;

  /**
   * Render total task and review statistics
   * Only shows when tasker has completed 20 or more tasks
   */
  const shouldRenderTaskAndReview = useMemo(() => {
    if ((taskDone ?? 0) >= 20) {
      return (
        <BlockView style={styles.info}>
          <BlockView style={styles.wrapTaskInfo}>
            <CText style={styles.txtTask} color={Colors.neutral600}>
              {t('TASK_DETAIL_TASK_DONE')}:{' '}
            </CText>
            <CText
              testID="taskDone"
              style={styles.txtHeaderNumber}
              semiBold
              color={Colors.neutral800}
            >
              {taskDone?.toString()}
            </CText>
          </BlockView>
          
          <BlockView style={styles.line} />

          <BlockView style={styles.wrapTaskInfo}>
            <CText style={styles.txtTask} color={Colors.neutral600}>
              {t('REVIEW')}:{' '}
            </CText>
            <CText
              semiBold
              style={styles.txtHeaderNumber}
              color={Colors.neutral800}
            >
              {numberOfReview?.toString()}
            </CText>
          </BlockView>
        </BlockView>
      );
    }
    return null;
  }, [taskDone, numberOfReview, t]);

  /**
   * Render premium banner for premium taskers
   */
  const shouldRenderPremiumBanner = useMemo(() => {
    if (isPremiumTasker === true) {
      return (
        <BlockView style={styles.banner}>
          <IconImage
            style={styles.premiumBanner}
            source={require('@images/iconPremiumBanner.png')}
            resizeMode="contain"
          />
        </BlockView>
      );
    }
    return null;
  }, [isPremiumTasker]);

  return (
    <BlockView style={styles.header}>
      <BlockView style={styles.centerContainer}>
        <BlockView>
          <Avatar
            containerStyle={styles.imageAvatar}
            size={SIZE_AVATAR}
            avatar={avatar}
            isPremiumTasker={isPremiumTasker}
            isFavouriteTasker={isFavouriteTasker}
            avatarFrameSettings={taskerSettings?.avatarFrame}
          />
        </BlockView>
        
        <CText
          testID="taskerName"
          semiBold
          size={FontSizes.SIZE_20}
          numberOfLines={1}
          style={styles.txtName}
          color={Colors.neutral800}
        >
          {name}
        </CText>
      </BlockView>

      <ConditionView
        condition={!!avgRating && avgRating !== '0.0'}
        viewTrue={
          <BlockView style={styles.rating}>
            <Rating numberRating={parseFloat(avgRating || '0')} size={16} />
          </BlockView>
        }
      />

      {shouldRenderPremiumBanner}

      {shouldRenderTaskAndReview}
    </BlockView>
  );
};
