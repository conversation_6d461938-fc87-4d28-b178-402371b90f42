import { Dimensions, StyleSheet } from 'react-native';
import { Colors, Spacing } from '@btaskee/design-system';

const { width } = Dimensions.get('window');
const SIZE_MEDAL_ITEM = Math.round(width / 4);
const SIZE_ICON_MEDAL = SIZE_MEDAL_ITEM / 1.5;

export const styles = StyleSheet.create({
  medal: {
    backgroundColor: Colors.neutralWhite,
    paddingVertical: Spacing.SPACE_16,
    marginTop: Spacing.SPACE_08,
  },
  
  medalContainer: {
    paddingHorizontal: Spacing.SPACE_16,
  },
  
  itemMedal: {
    width: SIZE_MEDAL_ITEM,
    alignItems: 'center',
    marginRight: Spacing.SPACE_08,
  },
  
  iconContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  iconMedal: {
    height: SIZE_ICON_MEDAL,
    width: SIZE_ICON_MEDAL,
    borderRadius: 8,
  },
  
  boxNumberOfMedal: {
    position: 'absolute',
    bottom: -5,
    right: -5,
    borderWidth: 2,
    borderRadius: 100,
    paddingHorizontal: Spacing.SPACE_04,
    paddingVertical: 1,
    borderColor: Colors.neutralWhite,
    backgroundColor: Colors.green500,
    minWidth: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  txtMedal: {
    marginTop: Spacing.SPACE_16,
    textAlign: 'center',
    lineHeight: 16,
  },
});
