/**
 * TaskerMedal component for displaying tasker badges and achievements
 * Migrated from legacy tasker-detail-medal.js with modern design system components
 */
import React from 'react';
import {
  BlockView,
  Colors,
  ConditionView,
  CText,
  FontSizes,
  IconImage,
  ScrollView,
  Spacing,
} from '@btaskee/design-system';
import { find } from 'lodash-es';

import { useI18n } from '@hooks';
import {
  ARR_QUESTION_5_STAR,
  BADGE_PREMIUM,
  BEAUTY_ARR_QUESTION_5_STAR,
} from '../../lib/constants/rating';

import { styles } from './styles';

interface IBadge {
  name: string;
  numOfBadges?: number;
}

interface TaskerMedalProps {
  badges: IBadge[];
}

/**
 * Get icon for badge from name
 * Searches through all available badge configurations
 */
const getIconBadgesFromName = (name: string) => {
  if (!name) {
    return null;
  }

  const badgeIcons = [
    ...BADGE_PREMIUM,
    ...ARR_QUESTION_5_STAR,
    ...BEAUTY_ARR_QUESTION_5_STAR,
  ];
  const icon = find(badgeIcons, { value: name });
  return icon?.icon || null;
};

/**
 * TaskerMedal component displays tasker badges and achievements
 * Shows badges in a horizontal scrollable list with icons and counts
 */
export const TaskerMedal: React.FC<TaskerMedalProps> = ({ badges }) => {
  const { t } = useI18n();

  // Don't render if no badges
  if (!badges || badges.length <= 0) {
    return null;
  }

  return (
    <BlockView style={styles.medal}>
      <ScrollView
        horizontal
        contentContainerStyle={styles.medalContainer}
        showsHorizontalScrollIndicator={false}
      >
        {badges.map((badge, index) => {
          const badgeIcon = getIconBadgesFromName(badge.name);

          return (
            <BlockView style={styles.itemMedal} key={index}>
              <BlockView style={styles.iconContainer}>
                <ConditionView
                  condition={!!badgeIcon}
                  viewTrue={
                    <IconImage
                      style={styles.iconMedal}
                      resizeMode="cover"
                      source={badgeIcon}
                    />
                  }
                />

                <ConditionView
                  condition={!!badge?.numOfBadges}
                  viewTrue={
                    <BlockView style={styles.boxNumberOfMedal}>
                      <CText
                        semiBold
                        size={FontSizes.SIZE_12}
                        numberOfLines={1}
                        color={Colors.neutralWhite}
                        testID={`numOfBadge${badge.name}`}
                      >
                        {badge?.numOfBadges}
                      </CText>
                    </BlockView>
                  }
                />
              </BlockView>

              <CText
                testID={badge.name}
                numberOfLines={2}
                style={styles.txtMedal}
                color={Colors.neutral800}
                size={FontSizes.SIZE_12}
              >
                {t(badge.name)}
              </CText>
            </BlockView>
          );
        })}
      </ScrollView>
    </BlockView>
  );
};
