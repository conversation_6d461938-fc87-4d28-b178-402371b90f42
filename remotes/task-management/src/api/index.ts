import { IRespond } from '@btaskee/design-system';

/**
 * API functions for TaskerDetail functionality
 * Uses the legacy API approach since these endpoints are not yet in the design system
 */

/**
 * Legacy API import for chooseTasker and getTaskerReviews
 * These will be migrated to design system endpoints in the future
 */
// @ts-ignore - Legacy API import
import { chooseTasker } from '@api/task-api';
// @ts-ignore - Legacy API import
import { getTaskerReviews } from '@api/user-api';

/**
 * Choose a tasker for a specific task
 * @param taskId - The task ID
 * @param taskerId - The tasker ID to choose
 * @returns Promise with API response
 */
export const chooseTaskerAPI = async (
  taskId: string,
  taskerId: string,
): Promise<IRespond<any>> => {
  try {
    const response = await chooseTasker(taskId, taskerId);
    return response;
  } catch (error) {
    console.error('Error in chooseTaskerAPI:', error);
    throw error;
  }
};

/**
 * Get tasker reviews and detailed information
 * @param taskerId - The tasker ID to get reviews for
 * @returns Promise with tasker reviews and details
 */
export const getTaskerReviewsAPI = async (
  taskerId: string,
): Promise<IRespond<any>> => {
  try {
    const response = await getTaskerReviews(taskerId);
    return response;
  } catch (error) {
    console.error('Error in getTaskerReviewsAPI:', error);
    throw error;
  }
};
