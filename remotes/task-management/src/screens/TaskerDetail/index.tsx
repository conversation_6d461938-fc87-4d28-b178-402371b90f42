/**
 * TaskerDetail screen component
 * Migrated from legacy tasker-detail/layout/index.js with modern design system components
 * Displays detailed information about a tasker including profile, badges, and reviews
 */
import React, { useEffect, useMemo } from 'react';
import { Dimensions } from 'react-native';
import {
  Alert,
  BlockView,
  Colors,
  ConditionView,
  CText,
  FontSizes,
  PrimaryButton,
  ScrollView,
  Spacing,
  TaskManagementRouteName,
  TaskManagementStackParamList,
} from '@btaskee/design-system';
import { RouteProp, useRoute } from '@react-navigation/native';

import { useI18n } from '@hooks';

import { TaskerHeader } from '../../components/TaskerHeader';
import { TaskerMedal } from '../../components/TaskerMedal';
import { TaskerReview } from '../../components/TaskerReview';
import {
  type IExtendedTaskDetail,
  type ITaskerUser,
  useShouldRenderChooseTasker,
  useTaskerDetailActions,
  useTaskerDetailStore,
} from '../../stores/useTaskerDetailStore';
import { styles } from './styles';

const { height } = Dimensions.get('window');

type TaskerDetailRouteProp = RouteProp<
  TaskManagementStackParamList,
  TaskManagementRouteName.TaskerDetail
>;

/**
 * TaskerDetail screen displays comprehensive tasker information
 * Preserves exact functionality from legacy component with modern architecture
 */
export const TaskerDetail: React.FC = () => {
  const { t } = useI18n();
  const route = useRoute<TaskerDetailRouteProp>();
  const { tasker, dataTask } = route.params || {};
  const settingSystem = undefined; // TODO: Add settingSystem to route params if needed

  // Store state
  const { taskerDetail, badges, isLoading, isChoosingTasker } =
    useTaskerDetailStore();

  // Store actions
  const { initializeTaskerDetail, getTaskerReviews, chooseTasker } =
    useTaskerDetailActions();

  // Determine if choose tasker button should be rendered
  const shouldRenderChooseTasker = useShouldRenderChooseTasker(
    dataTask as IExtendedTaskDetail,
    tasker as ITaskerUser,
  );

  /**
   * Initialize tasker detail data on component mount
   */
  useEffect(() => {
    if (tasker) {
      initializeTaskerDetail(tasker as ITaskerUser, settingSystem);
      getTaskerReviews(tasker as ITaskerUser, settingSystem);
    }
  }, [tasker, settingSystem, initializeTaskerDetail, getTaskerReviews]);

  /**
   * Handle choose tasker button press
   * Shows confirmation alert before proceeding
   */
  const handleChooseTasker = () => {
    const taskerUser = tasker as ITaskerUser;
    if (!dataTask?._id || !taskerUser?.taskerId) {
      Alert.alert.open({
        title: t('ERROR'),
        message: t('SOMETHING_WENT_WRONG'),
        actions: [{ text: t('OK') }],
      });
      return;
    }

    Alert.alert.open({
      title: t('CHOOSE_TASKER'),
      message: t('CHOOSE_TASKER_CONFIRM_MESSAGE'),
      actions: [
        {
          text: t('CANCEL'),
          style: 'cancel',
        },
        {
          text: t('CONFIRM'),
          onPress: () => {
            chooseTasker(
              dataTask._id,
              taskerUser.taskerId,
              () => {
                // Success callback - could navigate back or show success message
                Alert.alert.open({
                  title: t('SUCCESS'),
                  message: t('CHOOSE_TASKER_SUCCESS'),
                  actions: [{ text: t('OK') }],
                });
              },
              () => {
                // Error callback - show error message
                Alert.alert.open({
                  title: t('ERROR'),
                  message: t('CHOOSE_TASKER_ERROR'),
                  actions: [{ text: t('OK') }],
                });
              },
            );
          },
        },
      ],
    });
  };

  /**
   * Render choose tasker button
   */
  const renderChooseTaskerButton = useMemo(() => {
    if (!shouldRenderChooseTasker) {
      return null;
    }

    return (
      <BlockView style={styles.buttonContainer}>
        <Button
          testID="chooseTaskerButton"
          title={t('CHOOSE_TASKER')}
          onPress={handleChooseTasker}
          loading={isChoosingTasker}
          disabled={isChoosingTasker}
          style={styles.chooseButton}
        />
      </BlockView>
    );
  }, [shouldRenderChooseTasker, isChoosingTasker, handleChooseTasker, t]);

  return (
    <BlockView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Tasker Header with avatar, name, rating */}
        <TaskerHeader
          taskerDetail={taskerDetail}
          taskerSettings={settingSystem}
        />

        {/* Tasker Badges/Medals */}
        <ConditionView
          condition={badges.length > 0}
          viewTrue={<TaskerMedal badges={badges} />}
        />

        {/* Tasker Reviews */}
        <ConditionView
          condition={taskerDetail.reviews.length > 0}
          viewTrue={<TaskerReview reviews={taskerDetail.reviews} />}
        />

        {/* Loading indicator */}
        <ConditionView
          condition={isLoading}
          viewTrue={
            <BlockView style={styles.loadingContainer}>
              <CText
                color={Colors.neutral600}
                size={FontSizes.SIZE_14}
              >
                {t('LOADING')}
              </CText>
            </BlockView>
          }
        />
      </ScrollView>

      {/* Choose Tasker Button */}
      {renderChooseTaskerButton}
    </BlockView>
  );
};
