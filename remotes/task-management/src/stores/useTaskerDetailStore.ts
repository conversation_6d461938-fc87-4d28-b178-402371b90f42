import {
  <PERSON><PERSON>,
  createZustand,
  getAvgRating,
  IAcceptedTasker,
  ITaskDetail,
  IUser,
  Maybe,
  TASK_STATUS,
} from '@btaskee/design-system';
import { get } from 'lodash-es';

import { chooseTaskerAPI, getTaskerReviewsAPI } from '../api';

/**
 * Extended tasker type that includes properties from both IUser and IAcceptedTasker
 * This matches the legacy API response structure
 */
export type ITaskerUser = IUser & {
  taskerId?: string;
  isFavouriteTasker?: boolean;
};

/**
 * Extended task detail type that includes legacy properties
 */
export type IExtendedTaskDetail = ITaskDetail & {
  autoChooseTasker?: boolean;
};

/**
 * Badge interface for tasker achievements
 */
interface IBadge {
  name: string;
  numOfBadges?: number;
}

/**
 * Tasker detail information interface
 */
interface ITaskerDetailInfo {
  avgRating: string;
  taskDone: number;
  numberOfReview: number;
  reviews: string[];
  avatar?: string;
  name?: string;
  isPremiumTasker?: boolean;
  isFavouriteTasker?: boolean;
  badges?: IBadge[];
}

/**
 * Store state interface
 */
interface ITaskerDetailStore {
  // State
  taskerDetail: ITaskerDetailInfo;
  badges: IBadge[];
  isLoading: boolean;
  isChoosingTasker: boolean;

  // Actions
  setTaskerDetail: (detail: ITaskerDetailInfo) => void;
  setBadges: (badges: IBadge[]) => void;
  setIsLoading: (loading: boolean) => void;
  setIsChoosingTasker: (choosing: boolean) => void;
  resetStore: () => void;
}

/**
 * Business logic actions interface
 */
interface ITaskerDetailActions {
  getTaskerReviews: (
    tasker: Maybe<ITaskerUser>,
    settingSystem?: any,
  ) => Promise<void>;
  chooseTasker: (
    taskId: string,
    taskerId: string,
    onSuccess?: () => void,
    onError?: () => void,
  ) => Promise<void>;
  initializeTaskerDetail: (
    tasker: Maybe<ITaskerUser>,
    settingSystem?: any,
  ) => void;
  setIconBadgeIcon: (data: any) => void;
}

/**
 * Default tasker detail state
 */
const getDefaultTaskerDetail = (
  tasker: Maybe<ITaskerUser>,
  settingSystem?: any,
): ITaskerDetailInfo => ({
  avgRating: getAvgRating(
    tasker,
    settingSystem?.numberOfTaskCanSeeRatingTasker,
  ),
  taskDone: get(tasker, 'taskDone', 0),
  numberOfReview: get(tasker, 'taskDone', 0),
  reviews: [],
  avatar: get(tasker, 'avatar', ''),
  name: get(tasker, 'name', ''),
  isPremiumTasker: get(tasker, 'isPremiumTasker', false),
  isFavouriteTasker: get(tasker, 'isFavouriteTasker', false),
});

/**
 * TaskerDetail store using Zustand
 * Manages tasker information, badges, reviews, and API interactions
 */
export const useTaskerDetailStore = createZustand<ITaskerDetailStore>()(
  (set) => ({
    // Initial state
    taskerDetail: {
      avgRating: '0.0',
      taskDone: 0,
      numberOfReview: 0,
      reviews: [],
    },
    badges: [],
    isLoading: false,
    isChoosingTasker: false,

    // State setters
    setTaskerDetail: (detail: ITaskerDetailInfo) =>
      set({ taskerDetail: detail }),
    setBadges: (badges: IBadge[]) => set({ badges }),
    setIsLoading: (loading: boolean) => set({ isLoading: loading }),
    setIsChoosingTasker: (choosing: boolean) =>
      set({ isChoosingTasker: choosing }),
    resetStore: () =>
      set({
        taskerDetail: {
          avgRating: '0.0',
          taskDone: 0,
          numberOfReview: 0,
          reviews: [],
        },
        badges: [],
        isLoading: false,
        isChoosingTasker: false,
      }),
  }),
);

/**
 * Business logic actions for TaskerDetail
 * Handles API calls and complex state updates
 */
export const useTaskerDetailActions = (): ITaskerDetailActions => {
  const { setTaskerDetail, setBadges, setIsLoading, setIsChoosingTasker } =
    useTaskerDetailStore();

  /**
   * Initialize tasker detail with default values
   */
  const initializeTaskerDetail = (
    tasker: Maybe<ITaskerUser>,
    settingSystem?: any,
  ) => {
    const defaultDetail = getDefaultTaskerDetail(tasker, settingSystem);
    setTaskerDetail(defaultDetail);
  };

  /**
   * Set badge icons including premium tasker badge
   */
  const setIconBadgeIcon = (data: any) => {
    const newBadges = data?.badges || [];
    if (data.isPremiumTasker) {
      newBadges.unshift({ name: 'PREMIUM_TASKER' });
    }
    setBadges(newBadges);
  };

  /**
   * Fetch tasker reviews and update state
   */
  const getTaskerReviews = async (
    tasker: Maybe<ITaskerUser>,
    settingSystem?: any,
  ) => {
    if (!tasker?.taskerId) return;

    try {
      setIsLoading(true);
      const results = await getTaskerReviewsAPI(tasker.taskerId);

      if (results?.isSuccess && results.data) {
        const updatedDetail = {
          ...results.data,
          taskDone: results.data?.numberOfTaskDone || results.data?.taskDone,
          avgRating: getAvgRating(
            results.data,
            settingSystem?.numberOfTaskCanSeeRatingTasker,
          ),
          avatar: tasker.avatar,
          name: tasker.name,
          isPremiumTasker: tasker.isPremiumTasker,
          isFavouriteTasker: tasker.isFavouriteTasker,
        };

        setTaskerDetail(updatedDetail);
        setIconBadgeIcon(results.data);
      }
    } catch (error) {
      console.error('Error fetching tasker reviews:', error);
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Choose tasker for a task
   */
  const chooseTasker = async (
    taskId: string,
    taskerId: string,
    onSuccess?: () => void,
    onError?: () => void,
  ) => {
    try {
      setIsChoosingTasker(true);
      const results = await chooseTaskerAPI(taskId, taskerId);

      if (results?.isSuccess) {
        onSuccess?.();
      } else {
        Alert.alert.open({
          title: 'DIALOG_TITLE_INFORMATION',
          message: 'ERROR_TRY_AGAIN',
          actions: [{ text: 'CLOSE' }],
        });
        onError?.();
      }
    } catch (error) {
      console.error('Error choosing tasker:', error);
      Alert.alert.open({
        title: 'DIALOG_TITLE_INFORMATION',
        message: 'ERROR_TRY_AGAIN',
        actions: [{ text: 'CLOSE' }],
      });
      onError?.();
    } finally {
      setIsChoosingTasker(false);
    }
  };

  return {
    getTaskerReviews,
    chooseTasker,
    initializeTaskerDetail,
    setIconBadgeIcon,
  };
};

/**
 * Helper hook to check if choose tasker button should be rendered
 */
export const useShouldRenderChooseTasker = (
  dataTask: Maybe<IExtendedTaskDetail>,
  tasker: Maybe<ITaskerUser>,
): boolean => {
  if (!dataTask || !tasker) return false;

  // Show tasker choose button only for task WAITING_ASKER_CONFIRMATION
  if (dataTask.status !== TASK_STATUS.WAITING_ASKER_CONFIRMATION) {
    return false;
  }

  // If has autoChooseTasker then don't show button choose
  if (dataTask.autoChooseTasker) {
    return false;
  }

  return true;
};
