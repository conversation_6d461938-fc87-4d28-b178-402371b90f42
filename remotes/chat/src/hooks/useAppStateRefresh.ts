import { useCallback, useEffect, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';

/**
 * Configuration options for the useAppStateRefresh hook
 * Purpose: Defines callback functions and behavior for different app state changes
 */
interface UseAppStateRefreshOptions {
  /**
   * Callback function triggered when app becomes active
   * Purpose: Handle data refresh when user returns to the app
   */
  onAppBecomeActive?: () => void;

  /**
   * Callback function triggered when app goes to background
   * Purpose: Handle cleanup or state saving when app goes to background
   */
  onAppGoToBackground?: () => void;

  /**
   * Whether the hook should be enabled
   * Purpose: Allows conditional enabling/disabling of app state monitoring
   * @default true
   */
  enabled?: boolean;
}

/**
 * Return type for the useAppStateRefresh hook
 * Purpose: Provides current app state and manual trigger function
 */
interface UseAppStateRefreshReturn {
  /**
   * Current app state
   * Purpose: Provides access to the current app state for conditional logic
   */
  currentAppState: AppStateStatus;

  /**
   * Manually trigger the active callback
   * Purpose: Allows manual triggering of refresh logic outside of app state changes
   */
  triggerRefresh: () => void;
}

/**
 * Custom hook for handling app state changes and triggering refresh logic
 * Purpose: Provides a reusable way to handle data refresh when app becomes active
 * and cleanup when app goes to background, following the established patterns in the codebase
 *
 * @param options - Configuration options for app state handling
 * @returns Object containing current app state and manual trigger function
 *
 * @example
 * ```typescript
 * const { triggerRefresh } = useAppStateRefresh({
 *   onAppBecomeActive: () => {
 *     // Refresh chat messages
 *     getListChatMessage(params);
 *   },
 *   onAppGoToBackground: () => {
 *     // Mark messages as read
 *     markMessagesAsRead();
 *   },
 * });
 * ```
 */
export const useAppStateRefresh = (
  options: UseAppStateRefreshOptions = {},
): UseAppStateRefreshReturn => {
  const { onAppBecomeActive, onAppGoToBackground, enabled = true } = options;

  // Use ref to track current app state to avoid stale closures
  const currentAppStateRef = useRef<AppStateStatus>(AppState.currentState);

  /**
   * Handle app state changes
   * Purpose: Triggers appropriate callbacks based on app state transitions
   * Follows the same pattern as existing implementations in the codebase
   */
  const handleAppStateChange = useCallback(
    (nextAppState: AppStateStatus) => {
      const previousAppState = currentAppStateRef.current;
      currentAppStateRef.current = nextAppState;

      // App becoming active from background or inactive state
      if (
        (previousAppState === 'background' ||
          previousAppState === 'inactive') &&
        nextAppState === 'active'
      ) {
        onAppBecomeActive?.();
      }

      // App going to background or becoming inactive
      if (
        (nextAppState === 'background' || nextAppState === 'inactive') &&
        previousAppState === 'active'
      ) {
        onAppGoToBackground?.();
      }
    },
    [onAppBecomeActive, onAppGoToBackground],
  );

  /**
   * Manual trigger function for refresh logic
   * Purpose: Allows manual triggering of the active callback outside of app state changes
   */
  const triggerRefresh = useCallback(() => {
    onAppBecomeActive?.();
  }, [onAppBecomeActive]);

  // Set up app state listener
  useEffect(() => {
    if (!enabled) {
      return;
    }

    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    return () => {
      subscription?.remove();
    };
  }, [handleAppStateChange, enabled]);

  return {
    currentAppState: currentAppStateRef.current,
    triggerRefresh,
  };
};
