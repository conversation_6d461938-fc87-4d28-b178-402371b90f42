import React from 'react';
import {
  BlockView,
  Colors,
  CText,
  FontSizes,
  Spacing,
  TouchableOpacity,
} from '@btaskee/design-system';
import type { IMessage } from '@types';
import { MessageFrom } from '@types';

import { useI18n } from '@hooks';

import { styles } from './styles';

/**
 * Interface for TranslateButton component props
 * Purpose: Defines the properties needed for message translation functionality
 */
interface TranslateButtonProps {
  currentMessage?: IMessage;
  onPress: (message?: IMessage) => void;
  isTranslating?: boolean;
  userLanguage?: string;
  taskerLanguage?: string;
}

/**
 * TranslateButton component for message translation
 * Purpose: Displays translate button for messages from taskers in different languages
 * and handles translation state (translating, translated)
 * @param props - Component props containing message data and translation handlers
 * @returns {JSX.Element | null} React component displaying translate button or null if not needed
 */
export const TranslateButton = ({
  currentMessage,
  onPress,
  isTranslating = false,
  userLanguage,
  taskerLanguage,
}: TranslateButtonProps): React.JSX.Element | null => {
  const { t, locale } = useI18n();

  // Don't show translate button if:
  // - Message already has translated text
  // - Message is a prepared message (template)
  // - Message contains media (image, video, location)
  // - Message has request data (custom actions)
  // - Message is from system or asker
  // - User and tasker speak the same language
  if (
    !currentMessage ||
    currentMessage.translatedText ||
    currentMessage.preparedMessage ||
    currentMessage.image ||
    currentMessage.video ||
    currentMessage.location ||
    currentMessage.requestData ||
    currentMessage.from === MessageFrom.System ||
    currentMessage.from === MessageFrom.Asker ||
    !taskerLanguage ||
    userLanguage === taskerLanguage
  ) {
    return null;
  }

  /**
   * Handles translate button press
   * Purpose: Triggers translation process for the current message
   */
  const handlePress = () => {
    onPress(currentMessage);
  };

  // Determine button text based on translation state
  const getButtonText = () => {
    if (isTranslating) {
      return t('TRANSLATING');
    }

    // Get language name for display
    const languageKey = getLanguageKey(userLanguage);
    return t('TRANSLATE_TO', { t: languageKey });
  };

  /**
   * Gets language key for translation
   * Purpose: Maps language codes to translation keys
   */
  const getLanguageKey = (language?: string): string => {
    const languageMap: { [key: string]: string } = {
      vi: t('VIETNAMESE'),
      en: t('ENGLISH'),
      th: t('THAILAND'),
      id: t('INDONESIAN'),
      my: t('MALAYSIA'),
    };
    return languageMap[language || locale] || t('ENGLISH');
  };

  return (
    <BlockView style={styles.container}>
      <TouchableOpacity
        onPress={handlePress}
        style={styles.button}
        disabled={isTranslating}
      >
        <CText
          style={styles.buttonText}
          size={FontSizes.SIZE_12}
        >
          {getButtonText()}
        </CText>
      </TouchableOpacity>
    </BlockView>
  );
};
