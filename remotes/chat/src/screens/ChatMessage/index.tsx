// import locale for dayjs
import 'dayjs/locale/es';
import 'dayjs/locale/id';
import 'dayjs/locale/ko';
import 'dayjs/locale/ms';
import 'dayjs/locale/th';
import 'dayjs/locale/vi';

import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import { ActivityIndicator } from 'react-native';
import {
  Bubble,
  BubbleProps,
  Composer,
  ComposerProps,
  DayProps,
  GiftedChat,
  InputToolbar,
  InputToolbarProps,
  LoadEarlierProps,
  Message,
  MessageProps,
  MessageText,
  MessageTextProps,
  SendProps,
  SystemMessageProps,
  TimeProps,
} from 'react-native-gifted-chat';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  Alert,
  BlockView,
  ChatRouteName,
  ChatStackScreenProps,
  Colors,
  ConditionView,
  CText,
  DateTimeHelpers,
  EndpointKeys,
  FastImage,
  FontFamily,
  FontSizes,
  IconAssets,
  IconImage,
  MessageFrom,
  Spacing,
  TouchableOpacity,
  TypeFormatDate,
  useApiMutation,
  useAppStore,
  useUserStore,
} from '@btaskee/design-system';
import { useFocusEffect } from '@react-navigation/native';
import { type IMessage, MessageStatus, TypeCustomMessage } from '@types';
import { cloneDeep, findIndex, isEmpty } from 'lodash-es';

import {
  ChatHeader,
  ChatMessageSkeleton,
  MessageBySystem,
  MessageCustomView,
  MessageImage,
  MessageVideo,
  RenderSuggestions,
  SendImage,
  SendLocation,
  TaskNotFound,
  TimeGiftChat,
  TranslateButton,
  TranslatedMessage,
} from '@components';
import {
  useAppStateRefresh,
  useChatMessage,
  useChatWebSocket,
  useI18n,
} from '@hooks';
import { useConversationChatStore } from '@stores';

import { styles } from './styles.ts';

type ChatMessageProps = ChatStackScreenProps<ChatRouteName.ChatMessage>;

/**
 * ChatMessage component for displaying chat conversation
 * Purpose: Main chat screen that handles real-time messaging, media sharing,
 * custom message types, and system messages using GiftedChat integration
 * @returns {JSX.Element} React component displaying chat conversation
 */
const ChatMessageComponent = ({
  route,
}: ChatMessageProps): React.JSX.Element => {
  const { user } = useUserStore();
  const { locale, isoCode } = useAppStore();

  const { t } = useI18n();
  const insets = useSafeAreaInsets();

  // Memoize store selectors to prevent unnecessary re-renders
  const {
    chatId,
    task,
    memberIds,
    conversation,
    taskerInfo,
    messages,
    isTaskerFavorite,
    setChatId,
    setTask,
    setMemberIds,
    setIsTaskerFavorite,
    setTaskerInfo,
    setConversation,
    setMessages,
    resetMessageState,
  } = useConversationChatStore();

  // Memoize user data to prevent re-renders when user object reference changes
  const memoizedUser = useMemo(
    () => ({
      _id: user?._id || '',
      avatar: user?.avatar,
      name: user?.name || '',
    }),
    [user?._id, user?.avatar, user?.name],
  );

  // Use chat message hook for state management
  const {
    isLoading,
    loadEarlier,
    isLoadingEarlier,
    onSend,
    onLoadEarlier,
    messageContainerRef,
    getListChatMessage,
    convertMessagesToGiftChat,
  } = useChatMessage();

  // WebSocket integration for real-time messages
  const { isConnected } = useChatWebSocket();

  const { mutate: onTranslateMessage } = useApiMutation({
    key: EndpointKeys.translateMessage,
  });

  // Local state for input toolbar features (restored from original)
  const [isShow, setIsShow] = useState(false); // For task list modal
  const [translatingMessages, setTranslatingMessages] = useState<string[]>([]); // Track translating messages

  // Memoize route params to prevent unnecessary effect triggers
  const routeParams = useMemo(() => route.params, [route.params]);

  /**
   * Refresh chat data function
   * Purpose: Centralized function to refresh chat data, used both on focus and app state change
   */
  const refreshChatData = useCallback(() => {
    if (user?._id && isoCode) {
      getListChatMessage(
        {
          taskId: routeParams?.task?._id || task?._id,
          chatId: routeParams?.chatId || chatId,
          memberIds: routeParams?.memberIds || memberIds,
        },
        {
          onSuccess: (data) => {
            setConversation(data);
            setChatId(data?._id);

            if (data?.task) {
              setTask(data?.task);
            }
            let tasker = {};
            if (data?.taskerInfo) {
              tasker = data?.taskerInfo;
            }
            // Handle chat mới. Chỉ hỗ trợ chat 1vs1
            if (data?.members) {
              tasker =
                data?.members?.find(
                  (member) => member?._id !== memoizedUser?._id,
                ) || {};
            }
            setTaskerInfo(tasker);
            if (data?.messages) {
              const newMessages = data?.messages?.map((mess) => {
                return convertMessagesToGiftChat({
                  newMessage: mess,
                  conversation: data,
                });
              });

              setMessages(newMessages);
            }
          },
        },
      );
    }
  }, [
    user?._id,
    isoCode,
    chatId,
    memberIds,
    routeParams,
    task?._id,
    memoizedUser?._id,
    getListChatMessage,
    setConversation,
    setChatId,
    setTask,
    setTaskerInfo,
    setMessages,
    convertMessagesToGiftChat,
  ]);

  // App state refresh integration
  // Purpose: Automatically refresh chat data when app becomes active from background
  // This ensures users see the latest messages when returning to the app
  useAppStateRefresh({
    onAppBecomeActive: refreshChatData,
    enabled: Boolean(user?._id && isoCode), // Only enable when user is logged in
  });

  useEffect(() => {
    // Extract route parameters
    setChatId(routeParams?.chatId);
    setTask(routeParams?.task);
    setMemberIds(routeParams?.memberIds);
    setIsTaskerFavorite(Boolean(routeParams?.isTaskerFavorite));
    setTaskerInfo(routeParams?.taskerInfo);
  }, [
    routeParams?.chatId,
    routeParams?.isTaskerFavorite,
    routeParams?.memberIds,
    routeParams?.task,
    routeParams?.taskerInfo,
    setChatId,
    setIsTaskerFavorite,
    setMemberIds,
    setTask,
    setTaskerInfo,
  ]);

  // Separate effect for cleanup only when chatId actually changes
  useEffect(() => {
    return () => {
      if (routeParams?.chatId !== chatId) {
        resetMessageState();
      }
    };
  }, [chatId, resetMessageState, routeParams?.chatId]); // Only reset when chatId changes

  // Initialize chat info based on chatId, taskId, and task details
  useFocusEffect(
    useCallback(() => {
      refreshChatData();
    }, [refreshChatData]),
  );

  // Memoize stable values to prevent render function recreation
  const placeholderText = useMemo(
    () => t('CHAT_TYPE_MESSAGE_PLACE_HOLDER'),
    [t],
  );

  /**
   * Renders the message input composer
   * Purpose: Custom input field for typing messages with proper styling
   */
  const renderComposer = useCallback(
    (mess: ComposerProps) => {
      return (
        <Composer
          {...mess}
          textInputStyle={styles.composer}
          placeholder={placeholderText}
          placeholderTextColor={Colors.neutral400}
          multiline
          composerHeight={
            !mess?.text ? mess?.composerHeight : mess?.composerHeight * 3
          }
        />
      );
    },
    [placeholderText],
  );

  /**
   * Renders the send button
   * Purpose: Custom send button with icon
   */
  const renderSend = useCallback((props: SendProps<IMessage>) => {
    return (
      <ConditionView
        condition={Boolean(props?.text)}
        viewTrue={
          <TouchableOpacity
            style={styles.sendButton}
            onPress={() => props.onSend?.({ text: props.text?.trim() }, true)}
          >
            <IconImage
              source={IconAssets.icSend}
              size={24}
              color={Colors.orange500}
            />
          </TouchableOpacity>
        }
      />
    );
  }, []);

  /**
   * Renders message bubbles
   * Purpose: Custom styling for message bubbles
   */
  const renderBubble = useCallback((props: BubbleProps<IMessage>) => {
    return (
      <Bubble
        {...props}
        wrapperStyle={styles.bubble}
        textStyle={styles.bubbleText}
      />
    );
  }, []);

  // Memoize link styles to prevent recreation
  const linkStyles = useMemo(
    () => ({
      left: {
        color: Colors.neutral800,
        fontSize: FontSizes.SIZE_14,
        fontFamily: FontFamily.semiBold,
      },
      right: {
        color: Colors.neutral800,
        fontSize: FontSizes.SIZE_14,
        fontFamily: FontFamily.semiBold,
      },
    }),
    [],
  );

  // Memoize sending text to prevent recreation
  const sendingText = useMemo(() => t('CHAT_FAV.SENDING'), [t]);

  /**
   * Renders message text
   * Purpose: Custom text rendering for messages with link styling
   */
  const renderMessageText = useCallback(
    (mess: MessageTextProps<IMessage>) => {
      const { currentMessage } = mess;
      return (
        <BlockView>
          <MessageText
            {...mess}
            linkStyle={linkStyles}
          />
          <ConditionView
            condition={currentMessage?.status === MessageStatus.sending}
            viewTrue={
              <BlockView
                row
                horizontal
                justify="flex-end"
              >
                <ActivityIndicator
                  size={'small'}
                  color={Colors.neutral400}
                  style={styles.loading}
                />
                <CText
                  right
                  size={FontSizes.SIZE_12}
                  margin={{
                    right: Spacing.SPACE_08,
                    vertical: Spacing.SPACE_04,
                  }}
                >
                  {sendingText}
                </CText>
              </BlockView>
            }
          />
          {currentMessage?.translatedText &&
          currentMessage?.from === MessageFrom.Tasker ? (
            <TranslatedMessage translatedText={currentMessage.translatedText} />
          ) : null}
        </BlockView>
      );
    },
    [linkStyles, sendingText],
  );

  /**
   * Renders video messages
   * Purpose: Custom video message rendering with play controls
   */
  const renderMessageVideo = useCallback(
    ({ currentMessage }: { currentMessage: IMessage }) => {
      return <MessageVideo data={currentMessage} />;
    },
    [],
  );

  /**
   * Renders image messages
   * Purpose: Custom image message rendering with full screen capability
   */
  const renderMessageImage = useCallback(
    ({ currentMessage }: { currentMessage: IMessage }) => {
      return <MessageImage data={currentMessage} />;
    },
    [],
  );

  /**
   * Renders day separator
   * Purpose: Custom day separator styling
   */
  const renderDay = useCallback(
    (props: DayProps & { previousMessage: IMessage }) => {
      const { createdAt, previousMessage } = props;
      if (
        createdAt == null ||
        DateTimeHelpers.checkIsSame({
          firstDate: createdAt,
          secondDate: previousMessage?.createdAt,
          unit: 'day',
        })
      ) {
        return null;
      }
      return (
        <CText
          center
          size={FontSizes.SIZE_12}
          color={Colors.neutral400}
          margin={{ vertical: Spacing.SPACE_08 }}
        >
          {DateTimeHelpers.formatToString({
            date: createdAt,
            typeFormat: TypeFormatDate.DateMonthYearFull,
          })}
        </CText>
      );
      // return null;
    },
    [],
  );

  /**
   * Renders user avatar
   * Purpose: Custom avatar rendering with FastImage for optimal performance
   */
  const renderAvatar = useCallback((props: any) => {
    if (!props.currentMessage?.user?.avatar) return null;

    return (
      <BlockView style={styles.avatarContainer}>
        <FastImage
          source={{ uri: props.currentMessage.user.avatar }}
          style={styles.avatar}
          resizeMode="cover"
        />
      </BlockView>
    );
  }, []);

  /**
   * Renders custom message view
   * Purpose: Handles custom message types like location, phone calls, etc.
   */
  const renderCustomView = useCallback(
    (props: BubbleProps<IMessage>) => {
      return (
        <MessageCustomView
          {...props}
          chatId={chatId}
          taskId={task?._id}
          serviceName={conversation?.task?.serviceName}
          task={task}
          taskerInfo={taskerInfo}
        />
      );
    },
    [chatId, task, conversation?.task?.serviceName, taskerInfo],
  );

  /**
   * Renders system messages
   * Purpose: Handles system-generated messages
   */
  const renderSystemMessage = useCallback(
    (props: SystemMessageProps<IMessage>) => {
      return (
        <MessageBySystem
          {...props}
          chatId={chatId}
          taskerInfo={taskerInfo}
        />
      );
    },
    [chatId, taskerInfo],
  );

  /**
   * Renders scroll to bottom button
   * Purpose: Custom scroll to bottom button
   */
  const renderScrollToBottomComponent = useCallback(
    () => (
      <BlockView style={styles.scrollToBottomButton}>
        <IconImage
          source={IconAssets.icArrowDown}
          color={Colors.orange500}
        />
      </BlockView>
    ),
    [],
  );

  /**
   * Renders time for messages
   * Purpose: Custom time rendering with proper styling
   */
  const renderTime = (
    props: TimeProps<IMessage> & { nextMessage: IMessage },
  ) => {
    const { currentMessage, nextMessage } = props;
    // Chi hien thi voi tin nhan cuoi cung cung nguoi gui va khac ngay
    let isSameSender = true;
    if (
      nextMessage?.user?._id === currentMessage?.user?._id &&
      DateTimeHelpers.checkIsSame({
        firstDate: nextMessage?.createdAt,
        secondDate: currentMessage?.createdAt,
        unit: 'day',
      })
    ) {
      isSameSender = false;
    }

    // Chỉ render với thời gian ở tin nhắn cuối cùng mà thôi (Ngoại trừ các loại tin nhắn từ Hệ thống và tin nhắn đã đc custom)
    if (
      isEmpty(props?.currentMessage?.messageBySystem) &&
      !props?.currentMessage?.image &&
      !props?.currentMessage?.video &&
      ![
        TypeCustomMessage.IncreaseDuration,
        TypeCustomMessage.UpdateDetail,
        TypeCustomMessage.UpdateDateTime,
        TypeCustomMessage.TextAction,
      ].includes(props?.currentMessage?.type as TypeCustomMessage) &&
      isSameSender
    ) {
      return (
        <TimeGiftChat
          {...props}
          timeTextStyle={{
            left: {
              marginBottom: Spacing.SPACE_04,
              fontSize: FontSizes.SIZE_10,
              color: Colors.neutral400,
              fontFamily: FontFamily.medium,
            },
            right: {
              marginBottom: Spacing.SPACE_04,
              fontSize: FontSizes.SIZE_10,
              color: Colors.neutral400,
              fontFamily: FontFamily.medium,
            },
          }}
        />
      );
    }
    return null;
  };

  /**
   * Handles message translation
   * Purpose: Translates message from tasker's language to user's language
   */
  const handleTranslateMessage = useCallback(
    (message?: IMessage) => {
      if (!message?._id || !message.text) return;

      // Add message to translating list
      setTranslatingMessages((prev) => [...prev, String(message._id)]);

      onTranslateMessage(
        {
          chatId,
          text: message.text,
          messageId: message._id,
          language: locale,
        },
        {
          onSuccess: (data) => {
            const messageNewArr = cloneDeep(messages) || [];
            // Get index message in array
            const messageIndex = findIndex(messageNewArr, {
              _id: message?._id,
            });
            // Neu ton tai thi cap nhat lai phan da dich xong vao vi tri tin nhan tien tai
            if (messageIndex !== -1 && !isEmpty(messageNewArr)) {
              messageNewArr[messageIndex].translatedText = data?.translatedText;
            }
            // Luu lai tin nhan
            setMessages(messageNewArr);
          },
          onError: (error) => {
            setTranslatingMessages((prev) =>
              prev.filter((id) => id !== String(message._id)),
            );
          },
        },
      );
    },
    [setTranslatingMessages, onTranslateMessage, chatId, locale, messages],
  );

  /**
   * Enhanced message rendering with translation support
   * Purpose: Wraps default message with translation functionality
   */
  const renderMessage = useCallback(
    (mess: MessageProps<IMessage>) => {
      const { currentMessage } = mess;
      const isTranslating = translatingMessages.includes(
        String(currentMessage?._id),
      );

      return (
        <BlockView testID={currentMessage?.preparedMessage}>
          <Message {...mess} />

          {/* Translation button for tasker messages */}
          {currentMessage?.user?._id !== user?._id && (
            <TranslateButton
              currentMessage={currentMessage}
              onPress={handleTranslateMessage}
              isTranslating={isTranslating}
              userLanguage={locale}
              taskerLanguage={taskerInfo?.language}
            />
          )}
        </BlockView>
      );
    },
    [translatingMessages, user?._id, locale, taskerInfo?.language],
  );

  /**
   * Handles suggestion selection
   * Purpose: Sends selected suggestion as message and hides suggestions
   */
  const handleSuggestionPress = useCallback(
    (suggestion: string) => {
      const message = {
        _id: Math.random()?.toString(),
        text: t(suggestion),
        createdAt: new Date(),
        user: {
          _id: user?._id || '',
        },
      };
      onSend([message]);
      Alert.alert.close();
    },
    [onSend, t, user?._id],
  );

  /**
   * Handles closing task list (placeholder)
   * Purpose: Placeholder for task list functionality
   */
  const handleCloseTaskList = useCallback(() => {
    // TODO: Implement task list functionality
  }, []);

  /**
   * Renders input toolbar with additional features
   * Purpose: Custom input toolbar with image, location, and suggestion buttons
   */
  const renderInputToolbar = useCallback(
    (props: InputToolbarProps<IMessage> & { text: string }) => {
      return (
        <InputToolbar
          {...props}
          containerStyle={styles.inputToolbar}
          renderActions={() => (
            <ConditionView
              condition={!props?.text}
              viewTrue={
                <BlockView
                  row
                  margin={{ bottom: Spacing.SPACE_08, top: Spacing.SPACE_12 }}
                >
                  <TouchableOpacity
                    onPress={() => {
                      Alert.alert.open({
                        message: (
                          <RenderSuggestions onPress={handleSuggestionPress} />
                        ),
                        contentContainerStyle: {
                          marginTop: 0,
                          paddingHorizontal: Spacing.SPACE_12,
                        },
                      });
                    }}
                    style={styles.wrapPlus}
                  >
                    <IconImage
                      source={IconAssets.icPlus}
                      color={Colors.orange500}
                      size={32}
                    />
                  </TouchableOpacity>
                  <SendImage
                    isUsedCamera
                    onSend={onSend}
                    onCloseListTask={handleCloseTaskList}
                    userId={user?._id || ''}
                  />
                  <SendImage
                    onSend={onSend}
                    onCloseListTask={handleCloseTaskList}
                    userId={user?._id || ''}
                  />
                  <SendLocation
                    onSend={onSend}
                    onCloseListTask={handleCloseTaskList}
                    userId={user?._id || ''}
                  />
                </BlockView>
              }
            />
          )}
        />
      );
    },
    [onSend, handleCloseTaskList, user?._id, handleSuggestionPress],
  );

  /**
   * Handles modal close press
   * Purpose: Closes any open modals or overlays
   */
  const handleOnPressCloseModal = () => {
    setIsShow(false);
  };

  const renderLoadEarlier = (props: LoadEarlierProps) => {
    return (
      <ActivityIndicator
        {...props}
        style={styles.loadEarlier}
      />
    );
  };

  // Show loading state
  if (isLoading && isEmpty(messages)) {
    return <ChatMessageSkeleton />;
  }

  // Show error state if no chat ID
  if (!chatId && !task) {
    return <TaskNotFound />;
  }

  return (
    <BlockView
      inset={['bottom']}
      style={styles.container}
    >
      {/* Overlay for modal */}
      <ConditionView
        condition={isShow}
        viewTrue={
          <TouchableOpacity onPress={handleOnPressCloseModal}>
            <BlockView style={styles.overlay} />
          </TouchableOpacity>
        }
      />

      {/* Restored Original Header */}
      <ChatHeader
        taskerInfo={taskerInfo}
        task={task}
        isConnected={isConnected}
        confirmedTasksWithTasker={conversation?.confirmedTasksWithTasker}
        waitingTasksWithTasker={conversation?.waitingTasksWithTasker}
        isTaskerFavorite={isTaskerFavorite}
        isShow={isShow}
        setIsShow={setIsShow}
        chatId={chatId}
      />

      {/* Chat Messages */}
      <BlockView flex>
        <GiftedChat
          messageContainerRef={messageContainerRef}
          keyboardShouldPersistTaps="never"
          isScrollToBottomEnabled
          scrollToBottomComponent={renderScrollToBottomComponent}
          renderSend={renderSend}
          renderComposer={renderComposer}
          renderInputToolbar={renderInputToolbar}
          messages={messages || []}
          renderBubble={renderBubble}
          renderMessage={renderMessage}
          renderMessageVideo={renderMessageVideo}
          renderMessageImage={renderMessageImage}
          renderMessageText={renderMessageText}
          renderDay={renderDay}
          onSend={onSend}
          dateFormat={TypeFormatDate.DateFullWithDay}
          timeFormat={TypeFormatDate.DateFullWithDay}
          user={memoizedUser}
          renderAvatar={renderAvatar}
          renderCustomView={renderCustomView}
          locale={locale}
          bottomOffset={-insets.bottom + Spacing.SPACE_08}
          renderAvatarOnTop={true}
          showUserAvatar={false}
          renderTime={renderTime}
          renderSystemMessage={renderSystemMessage}
          infiniteScroll
          onLoadEarlier={onLoadEarlier}
          isLoadingEarlier={isLoadingEarlier}
          loadEarlier={loadEarlier}
          renderLoadEarlier={renderLoadEarlier}
          messagesContainerStyle={{
            paddingTop: isEmpty(task) ? insets?.top : Spacing.SPACE_04,
          }}
          invertibleScrollViewProps={{
            contentContainerStyle: {
              paddingVertical: 100,
            },
          }}
        />
      </BlockView>
    </BlockView>
  );
};

/**
 * Memoized ChatMessage component to prevent unnecessary re-renders
 * Purpose: Optimizes performance by preventing re-renders when props haven't changed
 */
export const ChatMessage = memo(ChatMessageComponent);
